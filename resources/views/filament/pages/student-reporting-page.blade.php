<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Report Filters Form -->
        <div class="bg-white rounded-lg shadow p-6">
            {{ $this->form }}
        </div>

        <!-- Report Content -->
        @if(!empty($this->reportData))
            <div class="space-y-6">
                @switch($this->data['report_type'] ?? 'overview')
                    @case('overview')
                        @include('filament.pages.reports.overview', ['data' => $this->reportData])
                        @break

                    @case('program_analysis')
                        @include('filament.pages.reports.program-analysis', ['data' => $this->reportData])
                        @break

                    @case('year_level_analysis')
                        @include('filament.pages.reports.year-level-analysis', ['data' => $this->reportData])
                        @break

                    @case('enrollment_trends')
                        @include('filament.pages.reports.enrollment-trends', ['data' => $this->reportData])
                        @break

                    @case('demographic_analysis')
                        @include('filament.pages.reports.demographic-analysis', ['data' => $this->reportData])
                        @break

                    @case('academic_performance')
                        @include('filament.pages.reports.academic-performance', ['data' => $this->reportData])
                        @break

                    @case('financial_analysis')
                        @include('filament.pages.reports.financial-analysis', ['data' => $this->reportData])
                        @break

                    @default
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Report Data</h3>
                            <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto">{{ json_encode($this->reportData, JSON_PRETTY_PRINT) }}</pre>
                        </div>
                @endswitch
            </div>
        @else
            <div class="bg-white rounded-lg shadow p-6">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No Report Data</h3>
                    <p class="mt-1 text-sm text-gray-500">Configure your report filters above to generate analytics data.</p>
                </div>
            </div>
        @endif
    </div>

    @push('scripts')
    <script>
        // Auto-refresh data when filters change
        document.addEventListener('DOMContentLoaded', function() {
            // Add any custom JavaScript for charts or interactive elements
            console.log('Student Reporting Page loaded');
        });
    </script>
    @endpush
</x-filament-panels::page>
