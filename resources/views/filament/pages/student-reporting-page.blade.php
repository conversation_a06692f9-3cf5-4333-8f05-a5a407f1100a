<x-filament-panels::page>
    <div class="space-y-6">
        @if(!empty($this->reportData))
            <!-- Academic Period Info -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Current Academic Period</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-300">
                            {{ $this->reportData['academic_period']['school_year'] ?? 'N/A' }} -
                            {{ $this->reportData['academic_period']['semester_label'] ?? 'N/A' }}
                        </p>
                    </div>
                    <div class="text-right">
                        <p class="text-xs text-gray-500 dark:text-gray-400">Generated: {{ $this->reportData['generated_at'] ?? now()->format('Y-m-d H:i:s') }}</p>
                    </div>
                </div>
            </div>

            <!-- Overview Statistics -->
            @if(isset($this->reportData['overview']))
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-2.25" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Students</dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ number_format($this->reportData['overview']['total_students'] ?? 0) }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">SHS Students</dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ number_format($this->reportData['overview']['total_shs_students'] ?? 0) }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Male Students</dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ number_format($this->reportData['overview']['gender_distribution']['male'] ?? 0) }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-pink-600 dark:text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Female Students</dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ number_format($this->reportData['overview']['gender_distribution']['female'] ?? 0) }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Courses Breakdown -->
            @if(isset($this->reportData['courses']) && !empty($this->reportData['courses']))
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Courses Breakdown</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($this->reportData['courses'] as $course)
                                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                                    <div class="flex items-center justify-between mb-4">
                                        <div>
                                            <h4 class="text-xl font-bold text-gray-900 dark:text-white">{{ $course['course_code'] }}</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-300">{{ $course['course_title'] }}</p>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($course['total_students']) }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">Students</div>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">Gender Split</div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                M: {{ $course['gender_distribution']['male'] ?? 0 }} |
                                                F: {{ $course['gender_distribution']['female'] ?? 0 }}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">Avg Age</div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ number_format($course['average_age'] ?? 0, 1) }}</div>
                                        </div>
                                    </div>

                                    @if(isset($course['year_levels']) && !empty($course['year_levels']))
                                        <div class="mt-4">
                                            <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">Year Level Distribution</div>
                                            <div class="flex flex-wrap gap-1">
                                                @foreach($course['year_levels'] as $year => $count)
                                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200">
                                                        {{ $year }}Y: {{ $count }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Year Levels Breakdown -->
            @if(isset($this->reportData['year_levels']) && !empty($this->reportData['year_levels']))
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Year Level Analysis</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            @foreach($this->reportData['year_levels'] as $yearLevel)
                                <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-6 border border-green-200 dark:border-green-700">
                                    <div class="text-center mb-4">
                                        <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($yearLevel['total_students']) }}</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ $yearLevel['year_label'] }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-2">Avg Age: {{ number_format($yearLevel['average_age'] ?? 0, 1) }}</div>
                                    </div>

                                    <div class="space-y-3">
                                        @if(isset($yearLevel['programs']) && !empty($yearLevel['programs']))
                                            <div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Programs</div>
                                                <div class="flex flex-wrap gap-1">
                                                    @foreach($yearLevel['programs'] as $program => $count)
                                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200">
                                                            {{ $program }}: {{ $count }}
                                                        </span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif

                                        <div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Gender</div>
                                            <div class="flex space-x-2">
                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200">
                                                    M: {{ $yearLevel['gender_distribution']['male'] ?? 0 }}
                                                </span>
                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-200">
                                                    F: {{ $yearLevel['gender_distribution']['female'] ?? 0 }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Demographics -->
            @if(isset($this->reportData['demographics']))
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Demographics Analysis</h3>

                        <!-- Age Statistics -->
                        @if(isset($this->reportData['demographics']['age_statistics']))
                            <div class="mb-6">
                                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Age Statistics</h4>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                                        <div class="text-xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($this->reportData['demographics']['age_statistics']['average'], 1) }}</div>
                                        <div class="text-sm text-blue-500 dark:text-blue-300">Average Age</div>
                                    </div>
                                    <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                                        <div class="text-xl font-bold text-green-600 dark:text-green-400">{{ number_format($this->reportData['demographics']['age_statistics']['median'], 1) }}</div>
                                        <div class="text-sm text-green-500 dark:text-green-300">Median Age</div>
                                    </div>
                                    <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center">
                                        <div class="text-xl font-bold text-yellow-600 dark:text-yellow-400">{{ $this->reportData['demographics']['age_statistics']['min'] }}</div>
                                        <div class="text-sm text-yellow-500 dark:text-yellow-300">Youngest</div>
                                    </div>
                                    <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
                                        <div class="text-xl font-bold text-purple-600 dark:text-purple-400">{{ $this->reportData['demographics']['age_statistics']['max'] }}</div>
                                        <div class="text-sm text-purple-500 dark:text-purple-300">Oldest</div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Age Groups -->
                        @if(isset($this->reportData['demographics']['age_groups']) && !empty($this->reportData['demographics']['age_groups']))
                            <div class="mb-6">
                                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Age Group Distribution</h4>
                                <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                                    @foreach($this->reportData['demographics']['age_groups'] as $ageGroup => $count)
                                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                                            <div class="text-lg font-medium text-gray-900 dark:text-white">{{ $count }}</div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $ageGroup }}</div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Enrollment Status -->
            @if(isset($this->reportData['overview']['enrollment_status']) && !empty($this->reportData['overview']['enrollment_status']))
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Enrollment Status</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            @foreach($this->reportData['overview']['enrollment_status'] as $status => $count)
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($count) }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ ucfirst($status) }}</div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

        @else
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No Report Data</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Unable to load analytics data. Please try refreshing the page.</p>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
