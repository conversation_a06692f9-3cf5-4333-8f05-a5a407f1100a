<?php

namespace App\Jobs;

use App\Services\StudentReportingService;
use App\Services\GeneralSettingsService;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class ExportStudentDataJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    protected array $filters;
    protected string $format;
    protected int $userId;
    protected string $jobId;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(array $filters, string $format, int $userId, string $jobId)
    {
        $this->filters = $filters;
        $this->format = $format;
        $this->userId = $userId;
        $this->jobId = $jobId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Get services
            $settingsService = app(GeneralSettingsService::class);
            $reportingService = new StudentReportingService($settingsService);

            // Generate the export
            $filePath = $reportingService->generateFilteredExport($this->filters, $this->format);

            // Store the job result
            $this->storeJobResult($filePath);

            Log::info('Export job completed successfully', [
                'job_id' => $this->jobId,
                'user_id' => $this->userId,
                'file_path' => $filePath,
            ]);

        } catch (\Exception $e) {
            // Log the error
            Log::error('Export job failed: ' . $e->getMessage(), [
                'job_id' => $this->jobId,
                'user_id' => $this->userId,
                'filters' => $this->filters,
                'format' => $this->format,
                'exception' => $e,
            ]);

            throw $e; // Re-throw to mark job as failed
        }
    }

    /**
     * Store information about the completed job
     */
    private function storeJobResult(string $filePath): void
    {
        $result = [
            'job_id' => $this->jobId,
            'user_id' => $this->userId,
            'filters' => $this->filters,
            'format' => $this->format,
            'file_path' => $filePath,
            'completed_at' => now()->toDateTimeString(),
        ];

        Storage::put('exports/jobs/' . $this->jobId . '.json', json_encode($result));
    }
}
