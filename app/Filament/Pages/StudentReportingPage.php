<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Services\StudentReportingService;
use App\Services\GeneralSettingsService;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;

class StudentReportingPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Student Reports & Analytics';

    protected static ?string $title = 'Student Reports & Analytics';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.student-reporting-page';

    public ?array $data = [];

    public array $reportData = [];

    protected StudentReportingService $reportingService;

    protected GeneralSettingsService $settingsService;

    public function mount(): void
    {
        $this->reportingService = app(StudentReportingService::class);
        $this->settingsService = app(GeneralSettingsService::class);

        // Initialize form with current academic period
        $this->form->fill([
            'school_year' => $this->settingsService->getCurrentSchoolYearString(),
            'semester' => $this->settingsService->getCurrentSemester(),
            'report_type' => 'overview',
        ]);

        // Load initial report data
        $this->loadReportData();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Report Filters')
                    ->description('Configure the parameters for your student analytics report')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                Select::make('report_type')
                                    ->label('Report Type')
                                    ->options([
                                        'overview' => 'Overview Analytics',
                                        'program_analysis' => 'Program Analysis (BSIT, BSBA, BSHM)',
                                        'year_level_analysis' => 'Year Level Analysis',
                                        'enrollment_trends' => 'Enrollment Trends',
                                        'demographic_analysis' => 'Demographic Analysis',
                                        'academic_performance' => 'Academic Performance',
                                        'financial_analysis' => 'Financial Analysis',
                                    ])
                                    ->default('overview')
                                    ->reactive()
                                    ->afterStateUpdated(fn () => $this->loadReportData()),

                                Select::make('school_year')
                                    ->label('School Year')
                                    ->options($this->settingsService->getAvailableSchoolYears())
                                    ->default($this->settingsService->getCurrentSchoolYearString())
                                    ->reactive()
                                    ->afterStateUpdated(fn () => $this->loadReportData()),

                                Select::make('semester')
                                    ->label('Semester')
                                    ->options($this->settingsService->getAvailableSemesters())
                                    ->default($this->settingsService->getCurrentSemester())
                                    ->reactive()
                                    ->afterStateUpdated(fn () => $this->loadReportData()),
                            ]),

                        Grid::make(2)
                            ->schema([
                                DatePicker::make('date_from')
                                    ->label('Date From')
                                    ->reactive()
                                    ->afterStateUpdated(fn () => $this->loadReportData()),

                                DatePicker::make('date_to')
                                    ->label('Date To')
                                    ->reactive()
                                    ->afterStateUpdated(fn () => $this->loadReportData()),
                            ]),
                    ])
                    ->collapsible(),
            ])
            ->statePath('data');
    }

    public function loadReportData(): void
    {
        $filters = $this->form->getState();
        $this->reportData = $this->reportingService->generateReport($filters);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('exportExcel')
                ->label('Export to Excel')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action('exportToExcel'),

            Action::make('exportPdf')
                ->label('Export to PDF')
                ->icon('heroicon-o-document-text')
                ->color('danger')
                ->action('exportToPdf'),

            Action::make('refreshData')
                ->label('Refresh Data')
                ->icon('heroicon-o-arrow-path')
                ->action('loadReportData'),
        ];
    }

    public function exportToExcel()
    {
        try {
            $filters = $this->form->getState();
            $filePath = $this->reportingService->exportToExcel($filters);

            Notification::make()
                ->title('Export Successful')
                ->body('Excel report has been generated successfully.')
                ->success()
                ->send();

            return Response::download(storage_path('app/' . $filePath))->deleteFileAfterSend();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Export Failed')
                ->body('Failed to generate Excel report: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function exportToPdf()
    {
        try {
            $filters = $this->form->getState();
            $filePath = $this->reportingService->exportToPdf($filters);

            Notification::make()
                ->title('Export Successful')
                ->body('PDF report has been generated successfully.')
                ->success()
                ->send();

            return Response::download(storage_path('app/' . $filePath))->deleteFileAfterSend();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Export Failed')
                ->body('Failed to generate PDF report: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public static function canAccess(): bool
    {
        return auth()->user()->can('view_student_reports') || auth()->user()->hasRole('admin');
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
