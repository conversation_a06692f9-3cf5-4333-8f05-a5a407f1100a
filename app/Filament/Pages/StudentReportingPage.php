<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Services\StudentReportingService;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Response;

class StudentReportingPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Student Analytics';

    protected static ?string $title = 'Student Analytics Dashboard';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.student-reporting-page';

    public array $reportData = [];

    protected ?StudentReportingService $reportingService = null;

    public function mount(): void
    {
        $this->reportingService = app(StudentReportingService::class);

        // Load report data
        $this->loadReportData();
    }

    public function loadReportData(): void
    {
        if (!$this->reportingService) {
            $this->reportingService = app(StudentReportingService::class);
        }

        $this->reportData = $this->reportingService->generateDashboardReport();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('exportExcel')
                ->label('Export to Excel')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action('exportToExcel'),

            Action::make('refreshData')
                ->label('Refresh Data')
                ->icon('heroicon-o-arrow-path')
                ->action('loadReportData'),
        ];
    }

    public function exportToExcel()
    {
        try {
            if (!$this->reportingService) {
                $this->reportingService = app(StudentReportingService::class);
            }

            $filePath = $this->reportingService->exportToExcel();

            Notification::make()
                ->title('Export Successful')
                ->body('Excel report has been generated successfully.')
                ->success()
                ->send();

            return Response::download(storage_path('app/' . $filePath))->deleteFileAfterSend();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Export Failed')
                ->body('Failed to generate Excel report: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // public static function canAccess(): bool
    // {
    //     return auth()->user()->can('view_student_reports') || auth()->user()->hasRole('admin');
    // }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
