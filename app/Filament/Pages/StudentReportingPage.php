<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Services\StudentReportingService;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Response;

class StudentReportingPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Student Analytics';

    protected static ?string $title = 'Student Analytics Dashboard';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.student-reporting-page';

    public array $reportData = [];

    protected ?StudentReportingService $reportingService = null;

    public function mount(): void
    {
        $this->reportingService = app(StudentReportingService::class);

        // Load report data
        $this->loadReportData();
    }

    public function loadReportData(): void
    {
        if (!$this->reportingService) {
            $this->reportingService = app(StudentReportingService::class);
        }

        $this->reportData = $this->reportingService->generateDashboardReport();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('exportData')
                ->label('Export Data')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->form([
                    Section::make('Export Options')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Select::make('format')
                                        ->label('Export Format')
                                        ->options([
                                            'csv' => 'Excel (CSV)',
                                            'pdf' => 'PDF Document',
                                        ])
                                        ->default('csv')
                                        ->required(),

                                    Select::make('course_filter')
                                        ->label('Course Filter')
                                        ->options([
                                            'all' => 'All Courses',
                                            'BSIT' => 'BSIT Only',
                                            'BSBA' => 'BSBA Only',
                                            'BSHM' => 'BSHM Only',
                                        ])
                                        ->default('all')
                                        ->required(),
                                ]),

                            Grid::make(2)
                                ->schema([
                                    Select::make('year_level_filter')
                                        ->label('Year Level Filter')
                                        ->options([
                                            'all' => 'All Year Levels',
                                            '1' => '1st Year Only',
                                            '2' => '2nd Year Only',
                                            '3' => '3rd Year Only',
                                            '4' => '4th Year Only',
                                        ])
                                        ->default('all')
                                        ->required(),

                                    Checkbox::make('preview_mode')
                                        ->label('Preview Only (First 10 Records)')
                                        ->default(false),
                                ]),
                        ])
                ])
                ->action('handleExport'),

            Action::make('refreshData')
                ->label('Refresh Data')
                ->icon('heroicon-o-arrow-path')
                ->action('loadReportData'),
        ];
    }

    public function handleExport(array $data): void
    {
        try {
            if (!$this->reportingService) {
                $this->reportingService = app(StudentReportingService::class);
            }

            $filters = [
                'course_filter' => $data['course_filter'],
                'year_level_filter' => $data['year_level_filter'],
                'preview_mode' => $data['preview_mode'] ?? false,
            ];

            if ($data['preview_mode']) {
                // Generate preview
                $preview = $this->reportingService->generateExportPreview($filters);

                Notification::make()
                    ->title('Export Preview')
                    ->body('Preview generated with ' . count($preview['students']) . ' records. Check the preview section below.')
                    ->info()
                    ->send();

                // Store preview in session or component property for display
                session(['export_preview' => $preview]);

            } else {
                // Queue the actual export
                $jobId = $this->reportingService->queueExport($filters, $data['format'], auth()->id());

                Notification::make()
                    ->title('Export Queued')
                    ->body('Your export has been queued for processing. You will be notified when it\'s ready.')
                    ->success()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Export Failed')
                ->body('Failed to process export: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // public static function canAccess(): bool
    // {
    //     return auth()->user()->can('view_student_reports') || auth()->user()->hasRole('admin');
    // }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
