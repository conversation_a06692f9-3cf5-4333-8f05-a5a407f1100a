<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\Course;
use App\Models\ClassEnrollment;
use App\Models\ShsStudent;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Collection;

final class StudentReportingService
{
    private GeneralSettingsService $settingsService;

    public function __construct(GeneralSettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    public function generateDashboardReport(): array
    {
        // Get current academic period from settings
        $currentSchoolYear = $this->settingsService->getCurrentSchoolYearString();
        $currentSemester = $this->settingsService->getCurrentSemester();

        return [
            'overview' => $this->generateOverviewStats($currentSchoolYear, $currentSemester),
            'courses' => $this->generateCourseBreakdown($currentSchoolYear, $currentSemester),
            'year_levels' => $this->generateYearLevelBreakdown($currentSchoolYear, $currentSemester),
            'demographics' => $this->generateDemographics($currentSchoolYear, $currentSemester),
            'academic_period' => [
                'school_year' => $currentSchoolYear,
                'semester' => $currentSemester,
                'semester_label' => $this->settingsService->getAvailableSemesters()[$currentSemester] ?? 'Unknown',
            ],
            'generated_at' => now()->format('Y-m-d H:i:s'),
        ];
    }

    private function generateOverviewStats(string $schoolYear, int $semester): array
    {
        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        // Total students
        $totalStudents = Student::whereIn('id', $enrolledStudentIds)
            ->whereNull('deleted_at')
            ->count();

        // Gender distribution
        $genderStats = Student::whereIn('id', $enrolledStudentIds)
            ->whereNull('deleted_at')
            ->selectRaw('gender, COUNT(*) as count')
            ->groupBy('gender')
            ->pluck('count', 'gender')
            ->toArray();

        // Enrollment status
        $enrollmentStats = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return [
            'total_students' => $totalStudents,
            'total_shs_students' => ShsStudent::count(),
            'gender_distribution' => [
                'male' => $genderStats['Male'] ?? 0,
                'female' => $genderStats['Female'] ?? 0,
                'total' => array_sum($genderStats),
            ],
            'enrollment_status' => $enrollmentStats,
        ];
    }

    private function generateCourseBreakdown(string $schoolYear, int $semester): array
    {
        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        // Get all courses with their enrolled students
        $courses = Course::with(['students' => function ($query) use ($enrolledStudentIds) {
                $query->whereIn('id', $enrolledStudentIds)->whereNull('deleted_at');
            }])
            ->orderBy('code')
            ->get()
            ->map(function ($course) use ($schoolYear, $semester) {
                $students = $course->students;

                // Year level breakdown
                $yearLevelBreakdown = $students->groupBy('academic_year')->map->count()->toArray();

                // Gender breakdown
                $genderBreakdown = $students->groupBy('gender')->map->count()->toArray();

                return [
                    'course_code' => $course->code,
                    'course_title' => $course->title,
                    'total_students' => $students->count(),
                    'year_levels' => $yearLevelBreakdown,
                    'gender_distribution' => [
                        'male' => $genderBreakdown['Male'] ?? 0,
                        'female' => $genderBreakdown['Female'] ?? 0,
                    ],
                    'average_age' => $students->avg('age') ?? 0,
                ];
            })
            ->filter(fn($course) => $course['total_students'] > 0); // Only show courses with students

        return $courses->toArray();
    }

    private function generateYearLevelBreakdown(string $schoolYear, int $semester): array
    {
        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        $yearLevels = [1, 2, 3, 4];

        $yearLevelData = collect($yearLevels)->map(function ($year) use ($enrolledStudentIds) {
            $students = Student::where('academic_year', $year)
                ->whereIn('id', $enrolledStudentIds)
                ->whereNull('deleted_at')
                ->with('course')
                ->get();

            // Program breakdown
            $programBreakdown = $students->groupBy('course.code')->map->count()->toArray();

            // Gender breakdown
            $genderBreakdown = $students->groupBy('gender')->map->count()->toArray();

            return [
                'year_level' => $year,
                'year_label' => $this->getYearLevelLabel($year),
                'total_students' => $students->count(),
                'programs' => $programBreakdown,
                'gender_distribution' => [
                    'male' => $genderBreakdown['Male'] ?? 0,
                    'female' => $genderBreakdown['Female'] ?? 0,
                ],
                'average_age' => $students->avg('age') ?? 0,
            ];
        })->filter(fn($yearLevel) => $yearLevel['total_students'] > 0);

        return $yearLevelData->toArray();
    }

    private function generateDemographics(string $schoolYear, int $semester): array
    {
        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        $students = Student::whereIn('id', $enrolledStudentIds)
            ->whereNull('deleted_at')
            ->get();

        // Age statistics
        $ages = $students->pluck('age')->filter();

        // Age groups
        $ageGroups = $students->groupBy(function ($student) {
            $age = $student->age;
            if ($age < 18) return 'Under 18';
            if ($age <= 20) return '18-20';
            if ($age <= 22) return '21-22';
            if ($age <= 25) return '23-25';
            return 'Over 25';
        })->map->count()->toArray();

        return [
            'age_statistics' => [
                'average' => $ages->avg() ?? 0,
                'median' => $ages->median() ?? 0,
                'min' => $ages->min() ?? 0,
                'max' => $ages->max() ?? 0,
            ],
            'age_groups' => $ageGroups,
        ];
    }

    private function getYearLevelLabel(int $year): string
    {
        return match ($year) {
            1 => '1st Year',
            2 => '2nd Year',
            3 => '3rd Year',
            4 => '4th Year',
            default => 'Unknown Year',
        };
    }

    public function exportToExcel(): string
    {
        $reportData = $this->generateDashboardReport();

        // Create CSV content
        $csvContent = "Student Analytics Report\n";
        $csvContent .= "Generated: " . $reportData['generated_at'] . "\n";
        $csvContent .= "Academic Period: " . $reportData['academic_period']['school_year'] . " - " . $reportData['academic_period']['semester_label'] . "\n\n";

        // Overview section
        $csvContent .= "OVERVIEW\n";
        $csvContent .= "Total Students," . $reportData['overview']['total_students'] . "\n";
        $csvContent .= "Total SHS Students," . $reportData['overview']['total_shs_students'] . "\n";
        $csvContent .= "Male Students," . $reportData['overview']['gender_distribution']['male'] . "\n";
        $csvContent .= "Female Students," . $reportData['overview']['gender_distribution']['female'] . "\n\n";

        // Courses section
        $csvContent .= "COURSES\n";
        $csvContent .= "Course Code,Course Title,Total Students,Male,Female,Average Age\n";
        foreach ($reportData['courses'] as $course) {
            $csvContent .= $course['course_code'] . "," . $course['course_title'] . "," . $course['total_students'] . "," . $course['gender_distribution']['male'] . "," . $course['gender_distribution']['female'] . "," . number_format($course['average_age'], 1) . "\n";
        }

        $fileName = 'student_analytics_' . date('Y-m-d_H-i-s') . '.csv';
        $filePath = 'exports/' . $fileName;

        Storage::put($filePath, $csvContent);

        return $filePath;
    }
}
